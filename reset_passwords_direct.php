<?php
/**
 * ملف تغيير كلمات المرور مباشرة في قاعدة البيانات
 * Direct Database Password Reset Script
 * 
 * تحذير: هذا الملف يغير كلمات المرور لجميع المستخدمين مباشرة في قاعدة البيانات!
 * Warning: This file directly changes passwords for ALL users in the database!
 */

// إعدادات قاعدة البيانات
$db_host = 'localhost';
$db_username = 'root';
$db_password = '';
$db_name = 'onlinexaminationci';

// كلمة المرور الجديدة
$new_password = '123456';

echo "<h2>تغيير كلمات المرور مباشرة في قاعدة البيانات</h2>";
echo "<h3>Direct Database Password Reset</h3>";
echo "<hr>";

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_username, $db_password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✓ تم الاتصال بقاعدة البيانات بنجاح / Database connected successfully</p>";
    
    // تشفير كلمة المرور باستخدام bcrypt (نفس الطريقة المستخدمة في Ion Auth)
    $hashed_password = password_hash($new_password, PASSWORD_BCRYPT, ['cost' => 10]);
    
    echo "<p><strong>كلمة المرور الجديدة: " . $new_password . "</strong></p>";
    echo "<p><strong>New password: " . $new_password . "</strong></p>";
    echo "<hr>";
    
    // الحصول على جميع المستخدمين
    $stmt = $pdo->query("SELECT id, username, email, first_name, last_name FROM users");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($users)) {
        echo "<p style='color: red;'>لم يتم العثور على أي مستخدمين في النظام</p>";
        echo "<p style='color: red;'>No users found in the system</p>";
        exit;
    }
    
    echo "<p><strong>عدد المستخدمين الموجودين: " . count($users) . "</strong></p>";
    echo "<p><strong>Number of users found: " . count($users) . "</strong></p>";
    echo "<hr>";
    
    $success_count = 0;
    $error_count = 0;
    
    // تحديث كلمة المرور لكل مستخدم
    foreach ($users as $user) {
        echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd;'>";
        echo "<strong>المستخدم / User: </strong>" . $user['first_name'] . " " . $user['last_name'];
        echo " (ID: " . $user['id'] . ", Email: " . $user['email'] . ", Username: " . $user['username'] . ")<br>";
        
        try {
            // تحديث كلمة المرور في قاعدة البيانات
            $update_stmt = $pdo->prepare("UPDATE users SET password = ?, remember_code = NULL, forgotten_password_code = NULL, forgotten_password_time = NULL WHERE id = ?");
            $result = $update_stmt->execute([$hashed_password, $user['id']]);
            
            if ($result && $update_stmt->rowCount() > 0) {
                echo "<span style='color: green;'>✓ تم تغيير كلمة المرور بنجاح / Password changed successfully</span>";
                $success_count++;
            } else {
                echo "<span style='color: orange;'>⚠ لم يتم تحديث أي صف / No rows updated</span>";
                $error_count++;
            }
        } catch (Exception $e) {
            echo "<span style='color: red;'>✗ فشل في تغيير كلمة المرور / Failed to change password</span>";
            echo "<br><span style='color: red;'>خطأ / Error: " . $e->getMessage() . "</span>";
            $error_count++;
        }
        echo "</div>";
    }
    
    echo "<hr>";
    echo "<h3>النتائج النهائية / Final Results:</h3>";
    echo "<p><strong style='color: green;'>نجح: " . $success_count . " مستخدم / Success: " . $success_count . " users</strong></p>";
    echo "<p><strong style='color: red;'>فشل: " . $error_count . " مستخدم / Failed: " . $error_count . " users</strong></p>";
    
    if ($success_count > 0) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; margin: 10px 0;'>";
        echo "<strong>تم تغيير كلمات المرور بنجاح!</strong><br>";
        echo "<strong>Passwords changed successfully!</strong><br>";
        echo "يمكن الآن لجميع المستخدمين تسجيل الدخول باستخدام كلمة المرور: <strong>" . $new_password . "</strong><br>";
        echo "All users can now login using password: <strong>" . $new_password . "</strong>";
        echo "</div>";
        
        echo "<h4>تفاصيل تسجيل الدخول / Login Details:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'><th>الاسم/Name</th><th>البريد الإلكتروني/Email</th><th>اسم المستخدم/Username</th><th>كلمة المرور/Password</th></tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['first_name'] . " " . $user['last_name'] . "</td>";
            echo "<td>" . $user['email'] . "</td>";
            echo "<td>" . $user['username'] . "</td>";
            echo "<td><strong>" . $new_password . "</strong></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ في الاتصال بقاعدة البيانات / Database connection error:</p>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}

echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; margin: 10px 0;'>";
echo "<strong>تحذير أمني / Security Warning:</strong><br>";
echo "تأكد من حذف هذا الملف بعد الانتهاء من استخدامه لأسباب أمنية<br>";
echo "Make sure to delete this file after use for security reasons";
echo "</div>";

?>
