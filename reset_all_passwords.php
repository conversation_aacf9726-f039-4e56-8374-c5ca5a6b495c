<?php
/**
 * ملف تغيير كلمات المرور لجميع المستخدمين إلى 123456
 * Reset All Users Passwords to 123456
 * 
 * تحذير: هذا الملف يغير كلمات المرور لجميع المستخدمين!
 * Warning: This file changes passwords for ALL users!
 */

// تحديد مسار CodeIgniter
define('BASEPATH', 'system/');
define('APPPATH', 'application/');
define('ENVIRONMENT', 'development');

// تحميل CodeIgniter
require_once 'index.php';

// الحصول على instance من CodeIgniter
$CI =& get_instance();

// تحميل Ion Auth library
$CI->load->library('ion_auth');

// تحميل database
$CI->load->database();

echo "<h2>تغيير كلمات المرور لجميع المستخدمين</h2>";
echo "<h3>Reset All Users Passwords</h3>";
echo "<hr>";

// كلمة المرور الجديدة
$new_password = '123456';

// الحصول على جميع المستخدمين
$users = $CI->ion_auth->users()->result();

if (empty($users)) {
    echo "<p style='color: red;'>لم يتم العثور على أي مستخدمين في النظام</p>";
    echo "<p style='color: red;'>No users found in the system</p>";
    exit;
}

echo "<p><strong>عدد المستخدمين الموجودين: " . count($users) . "</strong></p>";
echo "<p><strong>Number of users found: " . count($users) . "</strong></p>";
echo "<p><strong>كلمة المرور الجديدة: " . $new_password . "</strong></p>";
echo "<p><strong>New password: " . $new_password . "</strong></p>";
echo "<hr>";

$success_count = 0;
$error_count = 0;

// تحديث كلمة المرور لكل مستخدم
foreach ($users as $user) {
    echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd;'>";
    echo "<strong>المستخدم / User: </strong>" . $user->first_name . " " . $user->last_name;
    echo " (ID: " . $user->id . ", Email: " . $user->email . ")<br>";
    
    // استخدام reset_password من Ion Auth
    $result = $CI->ion_auth->reset_password($user->email, $new_password);
    
    if ($result) {
        echo "<span style='color: green;'>✓ تم تغيير كلمة المرور بنجاح / Password changed successfully</span>";
        $success_count++;
    } else {
        echo "<span style='color: red;'>✗ فشل في تغيير كلمة المرور / Failed to change password</span>";
        echo "<br><span style='color: red;'>خطأ / Error: " . $CI->ion_auth->errors() . "</span>";
        $error_count++;
    }
    echo "</div>";
}

echo "<hr>";
echo "<h3>النتائج النهائية / Final Results:</h3>";
echo "<p><strong style='color: green;'>نجح: " . $success_count . " مستخدم / Success: " . $success_count . " users</strong></p>";
echo "<p><strong style='color: red;'>فشل: " . $error_count . " مستخدم / Failed: " . $error_count . " users</strong></p>";

if ($success_count > 0) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; margin: 10px 0;'>";
    echo "<strong>تم تغيير كلمات المرور بنجاح!</strong><br>";
    echo "<strong>Passwords changed successfully!</strong><br>";
    echo "يمكن الآن لجميع المستخدمين تسجيل الدخول باستخدام كلمة المرور: <strong>" . $new_password . "</strong><br>";
    echo "All users can now login using password: <strong>" . $new_password . "</strong>";
    echo "</div>";
}

echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; margin: 10px 0;'>";
echo "<strong>تحذير أمني / Security Warning:</strong><br>";
echo "تأكد من حذف هذا الملف بعد الانتهاء من استخدامه لأسباب أمنية<br>";
echo "Make sure to delete this file after use for security reasons";
echo "</div>";

?>
